import React from "react";
import { Order } from "@/types/order";
import { useCurrency } from "@/contexts/CurrencyContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import OrderStatusBadge from "./OrderStatusBadge";
import { 
  Calendar, 
  CreditCard, 
  MapPin, 
  User, 
  Mail, 
  Phone,
  Package,
  Receipt
} from "lucide-react";

interface OrderSummaryProps {
  order: Order;
}

const OrderSummary: React.FC<OrderSummaryProps> = ({ order }) => {
  const { formatPrice } = useCurrency();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    }).format(date);
  };

  const getPaymentMethodLabel = (method: string) => {
    switch (method) {
      case "cash":
        return "Cash on Delivery";
      case "bank":
        return "Bank Transfer";
      default:
        return method.charAt(0).toUpperCase() + method.slice(1);
    }
  };

  return (
    <div className="space-y-6">
      {/* Order Header */}
      <Card>
        <CardHeader className="p-4 sm:p-6">
          <div className="flex flex-col gap-3 sm:gap-4">
            <div className="flex items-start justify-between gap-3">
              <div className="min-w-0 flex-1">
                <CardTitle className="text-lg sm:text-2xl truncate">
                  Order #{order.id.slice(-8).toUpperCase()}
                </CardTitle>
                <div className="flex items-center gap-2 text-muted-foreground mt-2 text-sm">
                  <Calendar size={14} className="flex-shrink-0" />
                  <span className="truncate">Placed on {formatDate(order.createdAt)}</span>
                </div>
              </div>
              <OrderStatusBadge status={order.status} size="lg" />
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Order Details Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        {/* Customer Information */}
        <Card>
          <CardHeader className="p-4 sm:p-6">
            <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
              <User size={18} className="flex-shrink-0" />
              <span className="truncate">Customer Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 sm:space-y-4 p-4 sm:p-6">
            <div>
              <div className="font-semibold text-base sm:text-lg truncate">
                {order.customer.firstName} {order.customer.lastName}
              </div>
            </div>
            <div className="flex items-center gap-2 text-muted-foreground text-sm">
              <Mail size={14} className="flex-shrink-0" />
              <span className="truncate">{order.customer.email}</span>
            </div>
            <div className="flex items-center gap-2 text-muted-foreground text-sm">
              <Phone size={14} className="flex-shrink-0" />
              <span className="truncate">{order.customer.phone}</span>
            </div>
          </CardContent>
        </Card>

        {/* Shipping Information */}
        <Card>
          <CardHeader className="p-4 sm:p-6">
            <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
              <MapPin size={18} className="flex-shrink-0" />
              <span className="truncate">Shipping Address</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4 sm:p-6">
            <div className="space-y-2">
              <div className="font-medium text-sm sm:text-base">{order.shipping.address}</div>
              <div className="text-muted-foreground text-sm">
                {order.shipping.city}
                {order.shipping.postalCode && `, ${order.shipping.postalCode}`}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Information */}
        <Card>
          <CardHeader className="p-4 sm:p-6">
            <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
              <CreditCard size={18} className="flex-shrink-0" />
              <span className="truncate">Payment Method</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center gap-2">
              <div className="font-medium text-sm sm:text-base truncate">
                {getPaymentMethodLabel(order.paymentMethod)}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Order Summary */}
        <Card>
          <CardHeader className="p-4 sm:p-6">
            <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
              <Receipt size={18} className="flex-shrink-0" />
              <span className="truncate">Order Summary</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 p-4 sm:p-6">
            <div className="flex justify-between text-sm sm:text-base">
              <span className="text-muted-foreground">Subtotal:</span>
              <span className="font-medium">{formatPrice(order.subtotal)}</span>
            </div>
            {order.discount > 0 && (
              <div className="flex justify-between text-green-600 text-sm sm:text-base">
                <span>Discount:</span>
                <span className="font-medium">-{formatPrice(order.discount)}</span>
              </div>
            )}
            <div className="border-t pt-3">
              <div className="flex justify-between items-center">
                <span className="font-semibold text-base sm:text-lg">Total:</span>
                <span className="font-bold text-lg sm:text-xl text-accent">
                  {formatPrice(order.total)}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground pt-2 border-t">
              <Package size={12} className="flex-shrink-0" />
              <span>
                {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default OrderSummary;
