import React from "react";
import { Order } from "@/types/order";
import { useCurrency } from "@/contexts/CurrencyContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import OrderStatusBadge from "./OrderStatusBadge";
import { 
  Calendar, 
  CreditCard, 
  MapPin, 
  User, 
  Mail, 
  Phone,
  Package,
  Receipt
} from "lucide-react";

interface OrderSummaryProps {
  order: Order;
}

const OrderSummary: React.FC<OrderSummaryProps> = ({ order }) => {
  const { formatPrice } = useCurrency();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    }).format(date);
  };

  const getPaymentMethodLabel = (method: string) => {
    switch (method) {
      case "cash":
        return "Cash on Delivery";
      case "bank":
        return "Bank Transfer";
      default:
        return method.charAt(0).toUpperCase() + method.slice(1);
    }
  };

  return (
    <div className="space-y-6">
      {/* Order Header */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-2xl">
                Order #{order.id.slice(-8).toUpperCase()}
              </CardTitle>
              <div className="flex items-center gap-2 text-muted-foreground mt-2">
                <Calendar size={16} />
                <span>Placed on {formatDate(order.createdAt)}</span>
              </div>
            </div>
            <OrderStatusBadge status={order.status} size="lg" />
          </div>
        </CardHeader>
      </Card>

      {/* Order Details Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User size={20} />
              Customer Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="font-semibold text-lg">
                {order.customer.firstName} {order.customer.lastName}
              </div>
            </div>
            <div className="flex items-center gap-2 text-muted-foreground">
              <Mail size={16} />
              <span>{order.customer.email}</span>
            </div>
            <div className="flex items-center gap-2 text-muted-foreground">
              <Phone size={16} />
              <span>{order.customer.phone}</span>
            </div>
          </CardContent>
        </Card>

        {/* Shipping Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin size={20} />
              Shipping Address
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="font-medium">{order.shipping.address}</div>
              <div className="text-muted-foreground">
                {order.shipping.city}
                {order.shipping.postalCode && `, ${order.shipping.postalCode}`}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard size={20} />
              Payment Method
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <div className="font-medium">
                {getPaymentMethodLabel(order.paymentMethod)}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Order Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Receipt size={20} />
              Order Summary
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Subtotal:</span>
              <span>{formatPrice(order.subtotal)}</span>
            </div>
            {order.discount > 0 && (
              <div className="flex justify-between text-green-600">
                <span>Discount:</span>
                <span>-{formatPrice(order.discount)}</span>
              </div>
            )}
            <div className="border-t pt-3">
              <div className="flex justify-between items-center">
                <span className="font-semibold text-lg">Total:</span>
                <span className="font-bold text-xl text-accent">
                  {formatPrice(order.total)}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground pt-2 border-t">
              <Package size={14} />
              <span>
                {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default OrderSummary;
