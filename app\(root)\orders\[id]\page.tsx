"use client";
import React, { useState, useEffect } from "react";
import { useOrders } from "@/contexts/OrderContext";
import { motion } from "framer-motion";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Loader2,
  Al<PERSON><PERSON>riangle,
  Download,
  Share2
} from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { toast } from "sonner";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import { Order } from "@/types/order";
import OrderSummary from "@/components/orders/OrderSummary";
import OrderItems from "@/components/orders/OrderItems";
import OrderTimeline from "@/components/orders/OrderTimeline";

const OrderDetailsPage = () => {
  const { id } = useParams();
  const router = useRouter();
  const { getOrder, cancelOrder } = useOrders();
  const [order, setOrder] = useState<Order | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCancelling, setIsCancelling] = useState(false);

  // Fetch order data
  useEffect(() => {
    const fetchOrder = async () => {
      setIsLoading(true);
      try {
        const orderData = await getOrder(id as string);
        setOrder(orderData);
        if (!orderData) {
          setError("Order not found");
        }
      } catch (error) {
        console.error("Error fetching order:", error);
        setError("Failed to load order details");
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrder();
  }, [id, getOrder]);

  // Loading state
  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="container mx-auto px-4 py-16 text-center">
          <Loader2
            size={48}
            className="animate-spin mx-auto mb-4 text-accent"
          />
          <h2 className="text-xl font-semibold mb-2">
            Loading order details...
          </h2>
          <p className="text-gray-500">
            Please wait while we fetch your order information.
          </p>
        </div>
      </ProtectedRoute>
    );
  }

  // Error or order not found
  if (error || !order) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <AlertTriangle size={64} className="text-yellow-500 mx-auto mb-4" />
        <h1 className="text-2xl font-bold mb-4">Order Not Found</h1>
        <p className="mb-8">
          {error ||
            "The order you're looking for doesn't exist or has been removed."}
        </p>
        <Link href="/orders">
          <Button variant="default">View All Orders</Button>
        </Link>
      </div>
    );
  }

  // Handle order cancellation
  const handleCancelOrder = async () => {
    if (!order || isCancelling) return;

    const confirmed = window.confirm(
      "Are you sure you want to cancel this order? This action cannot be undone."
    );

    if (confirmed) {
      setIsCancelling(true);
      try {
        await cancelOrder(order.id);
        // Refresh order data
        const updatedOrder = await getOrder(order.id);
        setOrder(updatedOrder);
        toast.success("Order cancelled successfully");
      } catch (error) {
        console.error("Error cancelling order:", error);
        toast.error("Failed to cancel order. Please try again.");
      } finally {
        setIsCancelling(false);
      }
    }
  };

  const handleShareOrder = async () => {
    if (!order) return;

    try {
      await navigator.share({
        title: `Order #${order.id.slice(-8).toUpperCase()}`,
        text: `Check out my order from Chinioti Wooden Art`,
        url: window.location.href,
      });
    } catch (error) {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
      toast.success("Order link copied to clipboard");
    }
  };

  const handleDownloadReceipt = () => {
    // This would typically generate and download a PDF receipt
    toast.info("Receipt download feature coming soon");
  };

  return (
    <ProtectedRoute>
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div className="flex items-center gap-4">
            <Link href="/orders">
              <Button variant="ghost" size="sm" className="gap-2">
                <ArrowLeft size={16} />
                Back to Orders
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Order Details</h1>
              <p className="text-gray-600 mt-1">
                View and manage your order information
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleShareOrder}
              className="gap-2"
            >
              <Share2 size={16} />
              Share
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadReceipt}
              className="gap-2"
            >
              <Download size={16} />
              Receipt
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
          {/* Order Summary and Items */}
          <div className="xl:col-span-3 space-y-8">
            <OrderSummary order={order} />
            <OrderItems items={order.items} />
          </div>

          {/* Sidebar */}
          <div className="xl:col-span-1 space-y-6">
            <OrderTimeline
              currentStatus={order.status}
              createdAt={order.createdAt}
              updatedAt={order.updatedAt}
            />

            {/* Action Buttons */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold mb-4">Actions</h3>
              <div className="space-y-3">
                {order.status === "pending" && (
                  <Button
                    variant="destructive"
                    className="w-full"
                    onClick={handleCancelOrder}
                    disabled={isCancelling}
                  >
                    {isCancelling ? (
                      <>
                        <Loader2 size={16} className="mr-2 animate-spin" />
                        Cancelling...
                      </>
                    ) : (
                      "Cancel Order"
                    )}
                  </Button>
                )}

                <Link href="/orders">
                  <Button variant="outline" className="w-full">
                    Back to Orders
                  </Button>
                </Link>

                <Link href="/products">
                  <Button variant="default" className="w-full">
                    Continue Shopping
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
};

export default OrderDetailsPage;
