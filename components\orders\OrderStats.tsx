import React from "react";
import { motion } from "framer-motion";
import { Order } from "@/types/order";
import { useCurrency } from "@/contexts/CurrencyContext";
import { Card, CardContent } from "@/components/ui/card";
import { 
  ShoppingBag, 
  Package, 
  Truck, 
  CheckCircle, 
  XCircle,
  DollarSign,
  TrendingUp
} from "lucide-react";

interface OrderStatsProps {
  orders: Order[];
  className?: string;
}

const OrderStats: React.FC<OrderStatsProps> = ({ orders, className = "" }) => {
  const { formatPrice } = useCurrency();

  const stats = React.useMemo(() => {
    const totalOrders = orders.length;
    const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);
    const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;
    
    const statusCounts = orders.reduce((acc, order) => {
      acc[order.status] = (acc[order.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalOrders,
      totalSpent,
      averageOrderValue,
      pending: statusCounts.pending || 0,
      processing: statusCounts.processing || 0,
      shipped: statusCounts.shipped || 0,
      delivered: statusCounts.delivered || 0,
      cancelled: statusCounts.cancelled || 0,
    };
  }, [orders]);

  const statCards = [
    {
      title: "Total Orders",
      value: stats.totalOrders.toString(),
      icon: ShoppingBag,
      color: "text-blue-600",
      bgColor: "bg-blue-100"
    },
    {
      title: "Total Spent",
      value: formatPrice(stats.totalSpent),
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-100"
    },
    {
      title: "Average Order",
      value: formatPrice(stats.averageOrderValue),
      icon: TrendingUp,
      color: "text-purple-600",
      bgColor: "bg-purple-100"
    },
    {
      title: "Delivered",
      value: stats.delivered.toString(),
      icon: CheckCircle,
      color: "text-emerald-600",
      bgColor: "bg-emerald-100"
    }
  ];

  const statusCards = [
    {
      title: "Pending",
      value: stats.pending,
      icon: Package,
      color: "text-yellow-600",
      bgColor: "bg-yellow-100"
    },
    {
      title: "Processing",
      value: stats.processing,
      icon: Package,
      color: "text-blue-600",
      bgColor: "bg-blue-100"
    },
    {
      title: "Shipped",
      value: stats.shipped,
      icon: Truck,
      color: "text-purple-600",
      bgColor: "bg-purple-100"
    },
    {
      title: "Cancelled",
      value: stats.cancelled,
      icon: XCircle,
      color: "text-red-600",
      bgColor: "bg-red-100"
    }
  ];

  if (orders.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Main Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {statCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        {stat.title}
                      </p>
                      <p className="text-2xl font-bold">
                        {stat.value}
                      </p>
                    </div>
                    <div className={`p-3 rounded-full ${stat.bgColor}`}>
                      <Icon size={24} className={stat.color} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Status Breakdown */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-4">Order Status Breakdown</h3>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            {statusCards.map((status, index) => {
              const Icon = status.icon;
              return (
                <motion.div
                  key={status.title}
                  className="flex items-center gap-3 p-3 rounded-lg border"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <div className={`p-2 rounded-full ${status.bgColor}`}>
                    <Icon size={16} className={status.color} />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      {status.title}
                    </p>
                    <p className="text-lg font-semibold">
                      {status.value}
                    </p>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OrderStats;
