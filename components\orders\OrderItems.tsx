import React from "react";
import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";
import { CartItem } from "@/types/cart";
import { useCurrency } from "@/contexts/CurrencyContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { IMAGE_PLACEHOLDER_URL } from "@/constants/helpers";
import { 
  Package, 
  ExternalLink, 
  AlertTriangle 
} from "lucide-react";

interface OrderItemsProps {
  items: CartItem[];
}

const OrderItems: React.FC<OrderItemsProps> = ({ items }) => {
  const { formatPrice } = useCurrency();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <Card>
      <CardHeader className="p-4 sm:p-6">
        <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
          <Package size={18} className="flex-shrink-0" />
          <span className="truncate">Order Items ({items.length})</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4 sm:p-6">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-4"
        >
          {items.map((item, index) => {
            // Handle invalid items
            if (!item || !item.product) {
              return (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="flex items-center gap-4 p-4 bg-red-50 border border-red-200 rounded-lg"
                >
                  <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                    <AlertTriangle size={24} className="text-red-500" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-red-900">
                      Product information unavailable
                    </h4>
                    <p className="text-sm text-red-700">
                      This item's data is missing or corrupted
                    </p>
                  </div>
                </motion.div>
              );
            }

            const product = item.product;
            const itemTotal = product.price * item.quantity;

            return (
              <motion.div
                key={product.id}
                variants={itemVariants}
                className="flex flex-col sm:flex-row gap-3 sm:gap-4 p-3 sm:p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
              >
                {/* Product Image and Basic Info */}
                <div className="flex gap-3 sm:gap-4">
                  <div className="flex-shrink-0">
                    <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gray-100 rounded-lg overflow-hidden">
                      <Image
                        src={product.images?.[0] || IMAGE_PLACEHOLDER_URL}
                        alt={product.name}
                        width={80}
                        height={80}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = IMAGE_PLACEHOLDER_URL;
                        }}
                      />
                    </div>
                  </div>

                  {/* Product Details */}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold text-base sm:text-lg leading-tight truncate">
                      {product.name}
                    </h4>
                    {product.description && (
                      <p className="text-xs sm:text-sm text-muted-foreground mt-1 line-clamp-2">
                        {product.description}
                      </p>
                    )}

                    {/* Product Details */}
                    <div className="flex flex-wrap items-center gap-3 sm:gap-4 mt-2 sm:mt-3 text-xs sm:text-sm text-muted-foreground">
                      <div>
                        <span className="font-medium">Qty:</span> {item.quantity}
                      </div>
                      <div>
                        <span className="font-medium">Unit:</span> {formatPrice(product.price)}
                      </div>
                      {product.category && (
                        <div className="hidden sm:block">
                          <span className="font-medium">Category:</span> {product.category}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Price and Actions */}
                <div className="flex items-center justify-between sm:flex-col sm:items-end gap-2 pt-2 sm:pt-0 border-t sm:border-t-0">
                  <div className="text-left sm:text-right">
                    <div className="font-bold text-base sm:text-lg text-accent">
                      {formatPrice(itemTotal)}
                    </div>
                    {item.quantity > 1 && (
                      <div className="text-xs text-muted-foreground">
                        {formatPrice(product.price)} each
                      </div>
                    )}
                  </div>

                  {/* View Product Link */}
                  <Link href={`/products/${product.id}`}>
                    <Button variant="outline" size="sm" className="text-xs min-h-[36px] px-3">
                      <ExternalLink size={12} className="mr-1" />
                      <span className="hidden sm:inline">View Product</span>
                      <span className="sm:hidden">View</span>
                    </Button>
                  </Link>
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Items Summary */}
        <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t">
          <div className="flex justify-between items-center text-sm text-muted-foreground">
            <span>Total Items:</span>
            <span className="font-medium">{items.reduce((sum, item) => sum + item.quantity, 0)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default OrderItems;
