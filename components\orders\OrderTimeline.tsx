import React from "react";
import { OrderStatus } from "@/types/order";
import { 
  Clock, 
  Package, 
  Truck, 
  CheckCircle, 
  XCircle 
} from "lucide-react";

interface OrderTimelineProps {
  currentStatus: OrderStatus;
  createdAt: string;
  updatedAt?: string;
}

interface TimelineStep {
  status: OrderStatus;
  label: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  description: string;
}

const timelineSteps: TimelineStep[] = [
  {
    status: "pending",
    label: "Order Placed",
    icon: Clock,
    description: "Your order has been received and is being processed"
  },
  {
    status: "processing",
    label: "Processing",
    icon: Package,
    description: "Your order is being prepared for shipment"
  },
  {
    status: "shipped",
    label: "Shipped",
    icon: Truck,
    description: "Your order is on its way to you"
  },
  {
    status: "delivered",
    label: "Delivered",
    icon: CheckCircle,
    description: "Your order has been successfully delivered"
  }
];

const OrderTimeline: React.FC<OrderTimelineProps> = ({
  currentStatus,
  createdAt,
  updatedAt
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    }).format(date);
  };

  const getStepStatus = (stepStatus: OrderStatus) => {
    const statusOrder = ["pending", "processing", "shipped", "delivered"];
    const currentIndex = statusOrder.indexOf(currentStatus);
    const stepIndex = statusOrder.indexOf(stepStatus);

    if (currentStatus === "cancelled") {
      return stepIndex === 0 ? "completed" : "cancelled";
    }

    if (stepIndex <= currentIndex) {
      return "completed";
    } else {
      return "pending";
    }
  };

  const getStepClasses = (status: string) => {
    switch (status) {
      case "completed":
        return {
          container: "text-green-600",
          icon: "bg-green-100 text-green-600 border-green-200",
          line: "bg-green-200"
        };
      case "cancelled":
        return {
          container: "text-red-600",
          icon: "bg-red-100 text-red-600 border-red-200",
          line: "bg-red-200"
        };
      default:
        return {
          container: "text-gray-400",
          icon: "bg-gray-100 text-gray-400 border-gray-200",
          line: "bg-gray-200"
        };
    }
  };

  // Handle cancelled orders
  if (currentStatus === "cancelled") {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <XCircle size={20} className="text-red-500" />
          Order Status
        </h3>
        <div className="flex items-center gap-4 p-4 bg-red-50 rounded-lg border border-red-200">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 rounded-full bg-red-100 border-2 border-red-200 flex items-center justify-center">
              <XCircle size={20} className="text-red-600" />
            </div>
          </div>
          <div>
            <div className="font-semibold text-red-900">Order Cancelled</div>
            <div className="text-sm text-red-700">
              This order was cancelled on {formatDate(updatedAt || createdAt)}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h3 className="text-lg font-semibold mb-6">Order Timeline</h3>
      
      <div className="space-y-6">
        {timelineSteps.map((step, index) => {
          const stepStatus = getStepStatus(step.status);
          const classes = getStepClasses(stepStatus);
          const Icon = step.icon;
          const isLast = index === timelineSteps.length - 1;

          return (
            <div key={step.status} className="relative">
              <div className="flex items-start gap-4">
                {/* Icon */}
                <div className="flex-shrink-0 relative">
                  <div className={`w-10 h-10 rounded-full border-2 flex items-center justify-center ${classes.icon}`}>
                    <Icon size={20} />
                  </div>
                  {/* Connecting line */}
                  {!isLast && (
                    <div className={`absolute top-10 left-1/2 transform -translate-x-1/2 w-0.5 h-6 ${classes.line}`} />
                  )}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className={`font-semibold ${classes.container}`}>
                    {step.label}
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    {step.description}
                  </div>
                  {stepStatus === "completed" && step.status === currentStatus && (
                    <div className="text-xs text-gray-500 mt-2">
                      {formatDate(updatedAt || createdAt)}
                    </div>
                  )}
                  {step.status === "pending" && stepStatus === "completed" && (
                    <div className="text-xs text-gray-500 mt-2">
                      {formatDate(createdAt)}
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Estimated delivery (for shipped orders) */}
      {currentStatus === "shipped" && (
        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center gap-2 text-blue-900 font-semibold">
            <Truck size={16} />
            Estimated Delivery
          </div>
          <div className="text-sm text-blue-700 mt-1">
            Your order should arrive within 3-5 business days
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderTimeline;
